<!DOCTYPE html>
<html lang="et" class="relative min-h-full light">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8" />
  <meta
    name="robots"
    content="max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
  <link rel="canonical" href="https://alimendid.ee/" />
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <meta
    name="description"
    content="Elatise õigusabi kiiresti ja lihtsalt. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />

  <meta name="twitter:site" content="@alimendidee" />
  <meta name="twitter:creator" content="@alimendidee" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta
    name="twitter:title"
    content="Alimendid.ee · Elatise õigusabi kiiresti ja lihtsalt" />
  <meta
    name="twitter:description"
    content="Elatise õigusabi kiiresti ja lihtsalt. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />
  <meta
    name="twitter:image"
    content="https://alimendid.ee/assets/failid/pildid/meta/logo/alimendid.jpg" />

  <meta property="og:url" content="https://alimendid.ee/" />
  <meta property="og:locale" content="et_EE" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="Alimendid.ee" />
  <meta
    property="og:title"
    content="Alimendid.ee · Elatise õigusabi kiiresti ja lihtsalt" />
  <meta
    property="og:description"
    content="Elatise õigusabi kiiresti ja lihtsalt. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />
  <meta
    property="og:image"
    content="https://alimendid.ee/assets/failid/pildid/meta/logo/alimendid.jpg" />

  <!-- Title -->
  <title>Alimendid.ee · Elatise õigusabi kiiresti ja lihtsalt</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/failid/favicon/favicon.ico" />

  <!-- Font -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    rel="stylesheet" />

  <!-- CSS HS -->
  <link href="./output.css" rel="stylesheet" />
  <link href="./assets/css/main.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="./assets/failid/css/disain.css">

  <!-- Replace: Light & dark kui on vaja -->

  <style>
    @keyframes marquee {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(calc(-256px * 8));
      }
    }

    @media (max-width: 768px) {
      @keyframes marquee {
        0% {
          transform: translateX(0);
        }

        100% {
          transform: translateX(calc(-160px * 8));
        }
      }
    }

    .animate-marquee {
      animation: marquee 40s linear infinite;
    }

    .animate-marquee:hover {
      animation-play-state: paused;
    }
  </style>

  <!-- Apexcharts kohustuslik CSS -->
  <link rel="stylesheet" href="../node_modules/apexcharts/dist/apexcharts.css">
</head>

<body class="dark:bg-neutral-900">
  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">

    <!-- Hero -->
    <div class="relative">

      <!-- Responsive Picture -->
      <picture class="absolute inset-0 z-0 w-full h-full">
        <!-- Desktop -->
        <source media="(min-width: 1280px)" srcset="./assets/failid/pildid/hero/avaleht-desktop.webp">
        <!-- Tablet -->
        <source media="(min-width: 768px)" srcset="./assets/failid/pildid/hero/avaleht-tablet.webp">
        <!-- Mobile fallback -->
        <img src="./assets/failid/pildid/hero/avaleht-mobile.webp"
          alt="Hero taust"
          class="w-full h-full object-cover"
          loading="eager"
          style="opacity: 0.9;">
      </picture>

      <div class="relative">

        <!-- Menu -->
        <?php include './assets/failid/komponendid/et/menu.php'; ?>

        <div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
          <!-- Grid -->
          <div class="flex justify-center items-center">

            <!-- Keskmine osa -->
            <div class="text-center max-w-3xl mx-auto">

              <!-- Pealkiri -->
              <div class="mt-2 md:mt-8 lg:mt-16 mb-6">

                <!-- Title -->
                <div class="mt-5">
                  <h1 class="block font-semibold text-gray-800 text-4xl md:text-5xl lg:text-6xl" style="line-height: 1.3;">
                    Elatise õigusabi <br> kiiresti ja lihtsalt
                  </h1>
                </div>

                <div class="mt-5 mx-auto max-w-xl">
                  <p class="text-xl md:text-2xl lg:text-2xl text-gray-600">Aitame Sind kõigis lapse elatise küsimustes</p>
                </div>

              </div>

              <!-- Nupp -->
              <div class="text-center">
                <a class="nupp-umar-noolega" href="#teenused">
                  Vali teenus
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </a>
              </div>

            </div>

          </div>
          <!-- End Grid -->
        </div>
      </div>
    </div>
    <!-- End Hero -->


    <!-- //LINK - Alusta -->
    <div id="teenused" class="py-14 lg:py-10 w-full max-w-5xl px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Title -->
        <div class="max-w-2xl mx-auto text-center mb-8">
          <h2 class="text-3xl md:text-4xl lg:text-4xl font-semibold md:leading-tight pb-1">Kuidas saame Sind aidata?</h2>
        </div>
        <!-- End Title -->
      </div>
      <?php include './assets/failid/komponendid/et/alusta/alusta.php'; ?>
    </div>

    <!-- //LINK - Numbrid -->
    <div
      class="relative pt-14 px-4 before:absolute before:inset-0 before:-z-1 before:mx-3 lg:before:mx-6 2xl:before:mx-10  before:rounded-2xl dark:before:from-neutral-800">
      <?php include './assets/failid/komponendid/et/numbrid/4-valge.php'; ?>
    </div>

    <!-- //LINK - Blogi -->
    <div class="py-14 lg:py-10 w-full max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="max-w-6xl px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
        <!-- Title -->
        <div class="max-w-2xl mx-auto text-center mb-10">
          <h2 class="text-3xl md:text-4xl lg:text-4xl font-semibold md:pb-1">Blogi</h2>
          <p class="mt-1 text-gray-600 text-lg">Alimendid.ee uudised ja blogi</p>
        </div>
        <!-- End Title -->
        <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <?php include './assets/failid/komponendid/et/blogi/3-viimast.php'; ?>
        </div>
        <div class="text-center">
          <a class="mt-5 py-3 px-4 inline-flex items-center gap-x-1 text-md font-medium rounded-full border border-gray-200 bg-white text-orange-500 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none" href="<?php echo $rootPath; ?>et/blogi.php">
            Rohkem postitusi
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6" />
            </svg>
          </a>
        </div>
      </div>
      <!-- End Card -->
    </div>

    <!-- //LINK - Banner -->
    <?php include './assets/failid/komponendid/et/bannerid/teenused/alusta/alusta.php'; ?>

  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- //LINK - FOOTER -->
  <?php include './assets/failid/komponendid/et/footer.php'; ?>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="./assets/vendor/lodash/lodash.min.js"></script>
  <script src="./assets/vendor/preline/dist/index.js"></script>
  <!-- Clipboard -->
  <script src="./assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="./assets/js/hs-copy-clipboard-helper.js"></script>

  <script>
    window.addEventListener("load", () => {
      (function() {
        const tabsId = "hs-pro-hero-tabs";
        const tabs = HSTabs.getInstance(`#${tabsId}`, true);
        const scrollNav = HSScrollNav.getInstance(
          "#hs-pro-hero-tabs-scroll",
          true
        );

        tabs.element.on("change", ({
          el
        }) => {
          scrollNav.element.centerElement(el);
        });

        window.addEventListener(
          "resize",
          _.debounce(() => {
            scrollNav.element.centerElement(tabs.element.current);
          }, 100)
        );

        window.addEventListener("change.hs.tab", ({
          detail
        }) => {
          if (detail.payload.tabsId !== tabsId) return false;

          const tabs = document.querySelector("#hs-pro-hero-tabs-scroll");

          window.scrollTo({
            top: tabs.offsetTop,
            behavior: "smooth",
          });
        });
      })();
    });
  </script>

  <!-- Apexcharts JavaScript -->
  <script src="../node_modules/lodash/lodash.min.js"></script>
  <!-- Apexcharts JavaScript -->
  <script src="../node_modules/apexcharts/dist/apexcharts.min.js"></script>
  <!-- Apexcharts Preline Helper JavaScript -->
  <script src="../node_modules/preline/dist/helper-apexcharts.js"></script>
</body>

</html>