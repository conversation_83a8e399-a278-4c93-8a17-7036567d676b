    <!-- Alimendid.ee andmebaas -->
    <?php require_once '../teenused/alimendid-teenused.php'; ?>

    <!DOCTYPE html>
    <html lang="et" class="relative min-h-full light">

    <head>
        <!-- Required Meta Tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
        <link rel="canonical" href="https://alimendid.ee/et/teenused/dokument.php">
        <meta name="description" content="Juristi koostatud elatise dokumendid kohtule esitamiseks. Hagi, vastulause, taotlus ja muud elatise dokumendid professionaalselt koostatud.">

        <!-- Twitter Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:site" content="@alimendid">
        <meta name="twitter:creator" content="@alimendid">
        <meta name="twitter:title" content="Elatise dokumendid · Alimendid.ee">
        <meta name="twitter:description" content="Juristi koostatud elatise dokumendid kohtule esitamiseks. Hagi, vastulause, taotlus ja muud elatise dokumendid professionaalselt koostatud.">
        <meta name="twitter:image" content="https://alimendid.ee/assets/failid/pildid/meta/logo/alimendid.jpg">

        <!-- Open Graph Meta Tags -->
        <meta property="og:url" content="https://alimendid.ee/et/teenused/dokument.php">
        <meta property="og:locale" content="et_EE">
        <meta property="og:type" content="website">
        <meta property="og:site_name" content="Alimendid.ee">
        <meta property="og:title" content="Elatise dokumendid · Alimendid.ee">
        <meta property="og:description" content="Juristi koostatud elatise dokumendid kohtule esitamiseks. Hagi, vastulause, taotlus ja muud elatise dokumendid professionaalselt koostatud.">
        <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/meta/logo/alimendid.jpg">

        <!-- Title -->
        <title>Elatise dokumendid · Alimendid.ee</title>

        <!-- Favicon -->
        <link rel="shortcut icon" href="../../assets/failid/favicon/favicon.ico">

        <!-- Font -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

        <!-- CSS HS -->
        <link href="../../output.css" rel="stylesheet" />
        <link rel="stylesheet" href="../../assets/css/main.min.css">
        <link rel="stylesheet" href="../../assets/failid/css/disain.css">

        <!-- Theme Check and Update -->
        <script>
            const html = document.querySelector('html');
            const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
            const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

            if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
            else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
            else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
            else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>

        <!-- ANCHOR ‧ Tags -->
        <!-- Google Ads-->
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'UA-221277240-1');
        </script>

        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-C8JJCED3EQ');
        </script>

        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
        </script>

        <!-- Hotjar -->
        <script>
            (function(h, o, t, j, a, r) {
                h.hj = h.hj || function() {
                    (h.hj.q = h.hj.q || []).push(arguments)
                };
                h._hjSettings = {
                    hjid: 3283746,
                    hjsv: 6
                };
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
        <style>
            /* Hero teksti joondus */
            .hero-text {
                text-align: center;
                /* vaikimisi mobiil */
            }

            @media (min-width: 768px) {
                .hero-text {
                    text-align: left !important;
                    /* md ja suurem */
                }
            }

            /* Link ainult md ja lg ekraanidel */
            .link-md-only {
                display: none !important;
                /* vaikimisi mobiil peidus */
            }

            @media (min-width: 768px) {
                .link-md-only {
                    display: inline-flex !important;
                    /* md ja lg nähtav */
                }
            }

            @media (min-width: 1024px) {
                .link-md-only {
                    display: inline-flex !important;
                    /* lg nähtav – sama reegel kehtib edasi */
                }
            }
        </style>
    </head>

    <body class="dark:bg-neutral-900">
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

        <!-- ========== HEADER ========== -->
        <?php include '../../assets/failid/komponendid/et/menu.php'; ?>
        <!-- ========== END HEADER ========== -->

        <!-- ========== MAIN CONTENT ========== -->
        <main id="content">

            <!-- //SECTION - Hero -->
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-3 sm:pb-5 md:pb-0 pt-0 md:pt-10 lg:pt-10">
                <!-- Grid -->
                <div class="grid md:grid-cols-2 md:items-center">

                    <!-- Pilt mobiilis esimesena, teistel ekraanidel teisena -->
                    <div class="relative ms-4 md:order-2">
                        <img class="w-full rounded-md" src="../../assets/failid/pildid/teenused/dokument.jpg" alt="Elatise dokument">
                    </div>

                    <!-- Tekstiveerg -->
                    <div class="md:order-1 hero-text mt-4">
                        <h1 class="mb-4 font-semibold text-gray-800 text-4xl md:text-5xl lg:text-6xl leading-none">
                            Elatise dokument
                        </h1>
                        <p class="text-gray-600 text-lg md:text-2xl">
                            Koostame Sinu vajadustele vastava elatise dokumendi
                        </p>

                        <div class="mt-7 grid gap-3 w-full sm:inline-flex">
                            <a class="nupp-kandiline-noolega" href="#paketid">
                                Vali pakett
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right-icon lucide-chevron-right">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </a>
                            <a class="py-3 px-4 justify-center items-center gap-x-2 text-md font-medium rounded-lg border border-gray-200 bg-white text-gray-800 hover:bg-gray-50 link-md-only" href="#sissejuhatus">
                                Loe lähemalt
                            </a>
                        </div>
                    </div>

                </div>
            </div>
            <!-- //!SECTION - Hero -->

            <!-- //LINK - 123 -->
            <div class="py-14 lg:py-10 w-full max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto mt-0 md:mt-5 lg:mt-5">
                <!-- Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-y-10 gap-x-4">
                    <!-- Icon Block -->
                    <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-clipboard-pen-icon lucide-clipboard-pen">
                            <rect width="8" height="4" x="8" y="2" rx="1" />
                            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5" />
                            <path d="M4 13.5V6a2 2 0 0 1 2-2h2" />
                            <path d="M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z" />
                        </svg>
                        <!-- //LINK ‧ 1 -->
                        <div class="mt-2 sm:mt-2">
                            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
                                1. Sisesta andmed
                            </h3>
                        </div>
                        <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
                            Sisesta enda andmed ja vasta lihtsatele küsimustele
                        </p>
                    </div>
                    <!-- End Icon Block -->

                    <!-- Icon Block -->
                    <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class="text-orange-500 mx-auto lucide lucide-notebook-pen-icon lucide-notebook-pen">
                            <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                            <path d="M15 18a3 3 0 1 0-6 0" />
                            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z" />
                            <circle cx="12" cy="13" r="2" />
                        </svg>
                        <!-- //LINK ‧ 2 -->
                        <div class="mt-2 sm:mt-2">
                            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
                                2. Dokumendi koostamine
                            </h3>
                        </div>
                        <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
                            Koostame täpselt Sinu vajadustele vastava dokumendi
                        </p>
                    </div>
                    <!-- End Icon Block -->

                    <!-- Icon Block -->
                    <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class="text-orange-500 mx-auto lucide lucide-notebook-pen-icon lucide-notebook-pen">
                            <path d="M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8" />
                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                            <path d="m16 19 2 2 4-4" />
                        </svg>
                        <!-- //LINK ‧ 2 -->
                        <div class="mt-2 sm:mt-2">
                            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
                                3. Saada edasi
                            </h3>
                        </div>
                        <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
                            Edasta dokument teisele vanemale või kohtule
                        </p>
                    </div>
                    <!-- End Icon Block -->
                </div>
                <!-- End Grid -->
            </div>
            <!-- Jooned -->
            <div class="mx-auto mb-3 hidden md:flex justify-center pointer-events-none">
                <img class="img-fluid" src="../../assets/failid/svg/jooned.svg" alt="Jooned">
            </div>

            <!-- //LINK - Tutvustus -->
            <div id="sissejuhatus" class="max-w-3xl px-4 pt-6 lg:pt-10 sm:px-6 lg:px-8 mx-auto">
                <!-- Hero -->
                <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
                    <!-- Heading -->
                    <div class="mb-8 max-w-xl mx-auto text-center">
                        <!-- //LINK ‧ Pealkiri -->
                        <h2 class="text-3xl font-semibold md:text-4xl md:leading-tight pb-1">Tutvustus</h2>
                        <!-- //LINK ‧ Kirjeldus -->
                        <p class="mt-1 text-gray-600 text-lg">Elatise sissenõudmise teenuse tutvustus</p>
                    </div>
                    <!-- End Heading -->
                </div>

                <!-- //LINK ‧ Tekst -->
                <div class="max-w-xl mx-auto px-4 text-left text-gray-700 space-y-4">
                    <p>Elatise dokumendi teenus tähendab juristi poolt juriidiliselt korrektse elatise dokumendi koostamist, mille abil saad nõuda elatist või sõlmida kokkuleppe teise vanemaga.</p>

                    <p>Teenust on mõistlik kasutada siis, kui plaanid nõuda elatist kohtuväliselt või kohtu kaudu, aga ka juhul, kui soovid vormistada kirjaliku kokkuleppe. Dokument koostatakse Sinu olukorrast ja vajadustest lähtudes.</p>

                    <p>Koostatud dokument sisaldab põhjalikku asjaolude kirjeldust, vajalikke viiteid seadustele ja kohtupraktikale. Vajadusel on Sul võimalik juristiga ühendust võtta ja menetluse käigus lisaküsimusi esitada.</p>

                    <p>Teenuse kasutamiseks sisesta oma andmed ja vasta küsimustele. Jurist tutvub Sinu olukorraga ning alustab dokumendi koostamist ühe tööpäeva jooksul pärast andmete esitamist.</p>

                </div>
            </div>

            <!-- //LINK - Banner -->
            <div class="max-w-[85rem] mx-auto text-center pb-20">
                <div class="max-w-xl mx-auto px-4">
                    <!-- //LINK - Nupp -->
                    <div class="text-center mt-8">
                        <a class="nupp-umar-noolega" href="#paketid">
                            Vali pakett
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m9 18 6-6-6-6" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- //LINK - Kliendid -->
            <div
                class="relative py-6 px-4 before:absolute before:inset-0 before:-z-1 before:mx-3 lg:before:mx-6 2xl:before:mx-10  before:rounded-2xl dark:before:from-neutral-800">
                <?php include '../../assets/failid/komponendid/et/kliendid/kliendid.php'; ?>
            </div>

            <!-- //LINK - Teenus -->
            <div class="relative py-10 md:pt-20 px-4 before:absolute before:inset-0 before:-z-1 dark:before:from-neutral-800">
                <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
                    <!-- Heading -->
                    <div class="mb-8 md:mb-16 max-w-xl mx-auto text-center">
                        <h2 class="text-2xl font-semibold md:text-4xl md:leading-tight pb-1">Mida teenus sisaldab?</h2>
                        <p class="mt-1 text-gray-600 text-lg">Elatise dokumendi teenuse sisu</p>
                    </div>

                    <!-- Grid -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-6 sm:gap-x-10 lg:gap-y-12 lg:gap-x-16">

                        <!-- Icon Block -->
                        <div class="flex gap-5">
                            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                                <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                                <path d="M10 9H8" />
                                <path d="M16 13H8" />
                                <path d="M16 17H8" />
                            </svg>
                            <!-- //LINK ‧ 4 -->
                            <div class="grow">
                                <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                                    Elatise dokument
                                </h4>
                                <p class="mt-1 text-gray-500 dark:text-neutral-500">
                                    Juriidiliselt korrektne elatise dokument vastavalt Sinu olukorrale ja vajadustele
                                </p>
                            </div>
                        </div>
                        <!-- End Icon Block -->

                        <!-- Icon Block -->
                        <div class="flex gap-5">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                                <path d="M13 8H7" />
                                <path d="M17 12H7" />
                            </svg>
                            <!-- //LINK ‧ 2 -->
                            <div class="grow">
                                <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                                    Juristi konsultatsioon
                                </h4>
                                <p class="mt-1 text-gray-500 dark:text-neutral-500">
                                    Juristi nõustamine ja tugi kogu elatise protsessi vältel
                                </p>
                            </div>
                        </div>
                        <!-- End Icon Block -->

                        <!-- Icon Block -->
                        <div class="flex gap-5">
                            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M11 14h1v4" />
                                <path d="M16 2v4" />
                                <path d="M3 10h18" />
                                <path d="M8 2v4" />
                                <rect x="3" y="4" width="18" height="18" rx="2" />
                            </svg>
                            <!-- //LINK ‧ 5 -->
                            <div class="grow">
                                <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                                    Alustame 1 tööpäevaga
                                </h4>
                                <p class="mt-1 text-gray-500 dark:text-neutral-500">
                                    Alustame dokumendi koostamist ühe tööpäeva jooksul pärast andmete esitamist
                                </p>
                            </div>
                        </div>
                        <!-- End Icon Block -->

                        <!-- Icon Block -->
                        <div class="flex gap-5">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500">
                                <rect width="16" height="20" x="4" y="2" rx="2" />
                                <line x1="8" x2="16" y1="6" y2="6" />
                                <line x1="16" x2="16" y1="14" y2="18" />
                                <path d="M16 10h.01" />
                                <path d="M12 10h.01" />
                                <path d="M8 10h.01" />
                                <path d="M12 14h.01" />
                                <path d="M8 14h.01" />
                                <path d="M12 18h.01" />
                                <path d="M8 18h.01" />
                            </svg>
                            <!-- //LINK ‧ 1 -->
                            <div class="grow">
                                <h3 class="font-medium text-gray-800 dark:text-neutral-200">
                                    Elatise arvutus
                                </h3>
                                <p class="mt-1 text-gray-500 dark:text-neutral-500">
                                    Dokument sisaldab korrektset elatise arvutust koos arvutuskäiguga
                                </p>
                            </div>
                        </div>
                        <!-- End Icon Block -->

                        <!-- Icon Block -->
                        <div class="flex gap-5">
                            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0" />
                                <path d="M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0" />
                            </svg>
                            <!-- //LINK ‧ 3 -->
                            <div class="grow">
                                <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                                    Juriidiliselt korrektne
                                </h4>
                                <p class="mt-1 text-gray-500 dark:text-neutral-500">
                                    Dokument sisaldab vajalikke viiteid seadusele ja kohtupraktikale
                                </p>
                            </div>
                        </div>
                        <!-- End Icon Block -->

                        <!-- Icon Block -->
                        <div class="flex gap-5">
                            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 13v8" />
                                <path d="M12 3v3" />
                                <path d="M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z" />
                            </svg>
                            <!-- //LINK ‧ 6 -->
                            <div class="grow">
                                <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                                    Juristi soovitused
                                </h4>
                                <p class="mt-1 text-gray-500 dark:text-neutral-500">
                                    Juristi juhised ja soovitused, kuidas dokumenti kasutada ja mida silmas pidada
                                </p>
                            </div>
                        </div>
                        <!-- End Icon Block -->

                    </div>
                    <!-- End Grid -->
                </div>
            </div>

            <!-- //LINK - Hind -->
            <div id="paketid"
                class="relative py-10 md:py-14 px-4 before:absolute before:inset-0 before:-z-1 before:mx-3 lg:before:mx-6 2xl:before:mx-10">
                <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
                    <!-- Heading -->
                    <div class="mb-8 md:mb-16 max-w-xl mx-auto text-center">
                        <h2 class="text-2xl font-semibold md:text-4xl md:leading-tight pb-1">Hind</h2>
                        <p class="mt-1 text-gray-600 text-lg">Elatise dokumendi teenuse hinnad</p>
                    </div>
                    <!-- End Heading -->
                </div>
                <div class="max-w-6xl pt-10 pb-12 px-4 sm:px-6 lg:px-8 mx-auto">
                    <!-- Paketid -->
                    <?php include '../../assets/failid/komponendid/et/hinnad/dokument.php'; ?>
                </div>
            </div>

        </main>
        <!-- ========== END MAIN CONTENT ========== -->

        <!-- //LINK - Footer -->
        <?php include '../../assets/failid/komponendid/et/footer.php'; ?>

        <!-- JS PLUGINS -->
        <!-- Required plugins -->
        <script src="../../assets/vendor/lodash/lodash.min.js"></script>
        <script src="../../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
        <!-- Clipboard -->
        <script src="../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
        <script src="../../assets/js/hs-copy-clipboard-helper.js"></script>


    </body>

    </html>