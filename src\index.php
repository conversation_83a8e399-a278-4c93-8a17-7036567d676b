<!DOCTYPE html>
<html lang="et" class="relative min-h-full light">

<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8" />
  <meta
    name="robots"
    content="max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
  <link rel="canonical" href="https://alimendid.ee/" />
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <meta
    name="description"
    content="Lihtsaim viis elatise nõudmiseks. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />

  <meta name="twitter:site" content="@alimendidee" />
  <meta name="twitter:creator" content="@alimendidee" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta
    name="twitter:title"
    content="Alimendid.ee · Lihtsaim viis elatise nõudmiseks" />
  <meta
    name="twitter:description"
    content="Lihtsaim viis elatise nõudmiseks. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />
  <meta
    name="twitter:image"
    content="https://alimendid.ee/assets/failid/pildid/meta/logo/alimendid.jpg" />

  <meta property="og:url" content="https://alimendid.ee/" />
  <meta property="og:locale" content="et_EE" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="Alimendid.ee" />
  <meta
    property="og:title"
    content="Alimendid.ee · Lihtsaim viis elatise nõudmiseks" />
  <meta
    property="og:description"
    content="Lihtsaim viis elatise nõudmiseks. Professionaalne abi elatise arvutamisel, nõudmisel ja sissenõudmisel. Konsultatsioonid, dokumendid ja õigusabi." />
  <meta
    property="og:image"
    content="https://alimendid.ee/assets/failid/pildid/meta/logo/alimendid.jpg" />

  <!-- Title -->
  <title>Alimendid.ee · Lihtsaim viis elatise nõudmiseks</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="./assets/failid/favicon/favicon.ico" />

  <!-- Font -->
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    rel="stylesheet" />

  <!-- CSS HS -->
  <link href="./output.css" rel="stylesheet" />
  <link href="./assets/css/main.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="./assets/failid/css/disain.css">

  <!-- Replace: Light & dark kui on vaja -->

  <style>
    @keyframes marquee {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(calc(-256px * 8));
      }
    }

    @media (max-width: 768px) {
      @keyframes marquee {
        0% {
          transform: translateX(0);
        }

        100% {
          transform: translateX(calc(-160px * 8));
        }
      }
    }

    .animate-marquee {
      animation: marquee 40s linear infinite;
    }

    .animate-marquee:hover {
      animation-play-state: paused;
    }
  </style>

  <!-- Apexcharts kohustuslik CSS -->
  <link rel="stylesheet" href="../node_modules/apexcharts/dist/apexcharts.css">
</head>

<body class="">
  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">

    <!-- Hero -->
    <div class="relative">

      <!-- Responsive Picture -->
      <picture class="absolute inset-0 z-0 w-full h-full">
        <!-- Desktop -->
        <source media="(min-width: 1280px)" srcset="./assets/failid/pildid/hero/avaleht-desktop.webp">
        <!-- Tablet -->
        <source media="(min-width: 768px)" srcset="./assets/failid/pildid/hero/avaleht-tablet.webp">
        <!-- Mobile fallback -->
        <img src="./assets/failid/pildid/hero/avaleht-mobile.webp"
          alt="Hero taust"
          class="w-full h-full object-cover"
          loading="eager"
          style="opacity: 0.9;">
      </picture>

      <div class="relative">

        <!-- Menu -->
        <?php include './assets/failid/komponendid/et/menu.php'; ?>

        <div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
          <!-- Grid -->
          <div class="flex justify-center items-center">

            <!-- Keskmine osa -->
            <div class="text-center max-w-3xl mx-auto">

              <!-- Pealkiri -->
              <div class="mt-2 md:mt-8 lg:mt-16 mb-6">

                <!-- Title -->
                <div class="mt-5">
                  <h1 class="block font-semibold text-gray-800 text-4xl md:text-5xl lg:text-6xl" style="line-height: 1.3;">
                    <!-- //LINK - Headline -->
                    Lihtsaim viis <br>elatise nõudmiseks
                  </h1>
                </div>

                <div class="mt-5 mx-auto max-w-xl">
                  <p class="text-xl md:text-2xl lg:text-2xl 
                  text-gray-600">
                    <!-- //LINK - Subheadline -->
                    Koostame vajalikud dokumendid elatisraha nõudmiseks
                  </p>
                </div>

              </div>

              <!-- Nupp -->
              <div class="text-center">
                <a class="nupp-umar-noolega" href="#123">
                  Alusta siit
                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </a>
              </div>

            </div>

          </div>
          <!-- End Grid -->
        </div>
      </div>
    </div>
    <!-- End Hero -->

    <!-- //LINK - 123 -->
    <div class="py-14 lg:py-10 w-full max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto mt-0 md:mt-5 lg:mt-5">
      <!-- Title -->
      <div class="max-w-2xl mx-auto text-center mb-10">
        <h2 class="text-3xl md:text-4xl lg:text-4xl font-semibold md:pb-1">Kiire. Lihtne. Usaldusväärne.</h2>
      </div>
      <!-- End Title -->
      <!-- Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-y-10 gap-x-4">

        <!-- Icon Block -->
        <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-clipboard-pen-icon lucide-clipboard-pen">
            <rect width="8" height="4" x="8" y="2" rx="1" />
            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5" />
            <path d="M4 13.5V6a2 2 0 0 1 2-2h2" />
            <path d="M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z" />
          </svg>
          <div class="mt-2 sm:mt-2">
            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
              1. Sisesta andmed
            </h3>
          </div>
          <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
            Sisesta oma andmed ja vasta mõnele lihtsale küsimusele
          </p>
        </div>
        <!-- End Icon Block -->

        <!-- Icon Block -->
        <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-orange-500 mx-auto lucide lucide-notebook-pen-icon lucide-notebook-pen">
            <path d="M14 2v4a2 2 0 0 0 2 2h4" />
            <path d="M15 18a3 3 0 1 0-6 0" />
            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z" />
            <circle cx="12" cy="13" r="2" />
          </svg>
          <div class="mt-2 sm:mt-2">
            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
              2. Vaata dokumenti
            </h3>
          </div>
          <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
            Koostame Sulle olukorrale vastava elatise dokumendi
          </p>
        </div>
        <!-- End Icon Block -->

        <!-- Icon Block -->
        <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-orange-500 mx-auto lucide lucide-notebook-pen-icon lucide-notebook-pen">
            <path d="M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8" />
            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
            <path d="m16 19 2 2 4-4" />
          </svg>
          <div class="mt-2 sm:mt-2">
            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
              3. Esita dokument
            </h3>
          </div>
          <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
            Saada dokument teisele vanemale või esita kohtule
          </p>
        </div>
        <!-- End Icon Block -->
      </div>
      <!-- End Grid -->
    </div>
    <!-- Jooned -->
    <div class="mx-auto mb-3 hidden md:flex justify-center pointer-events-none">
      <img class="img-fluid" src="./assets/failid/svg/jooned.svg" alt="Jooned">
    </div>


    <!-- //LINK - Eelised -->
    <div class="relative py-10 md:pt-20 px-4 before:absolute before:inset-0 before:-z-1 dark:before:from-neutral-800">
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Heading -->
        <div class="mb-8 md:mb-16 max-w-xl mx-auto text-center">
          <h2 class="text-2xl font-semibold md:text-4xl md:leading-tight pb-1">Meie eelised</h2>
          <p class="mt-1 text-gray-600 text-lg" style="display: none;">Alimendid.ee teenuse eelised</p>
        </div>

        <!-- Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-6 sm:gap-x-10 lg:gap-y-12 lg:gap-x-16">

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
              <path d="M14 2v4a2 2 0 0 0 2 2h4" />
              <path d="M10 9H8" />
              <path d="M16 13H8" />
              <path d="M16 17H8" />
            </svg>
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Lihtne
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Sisesta vajalikud andmed ja saad dokumendi kohe kätte
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500">
              <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z" />
            </svg>
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Kiire
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Kui valid juristi koostatud dokumendi, alustame tööga 1 päeva jooksul
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2v4a2 2 0 0 0 2 2h4" />
              <path d="M15 18a3 3 0 1 0-6 0" />
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z" />
              <circle cx="12" cy="13" r="2" />
            </svg>
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Personaalne
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Dokument arvestab täpselt Sinu olukorda ja lapse vajadusi
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500">
              <path d="M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0" />
              <path d="M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0" />
            </svg>
            <div class="grow">
              <h3 class="font-medium text-gray-800 dark:text-neutral-200">
                Kindel
              </h3>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Dokument sisaldab vajalikke viiteid seadustele ja kohtupraktikale
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10" />
              <path d="m4.93 4.93 4.24 4.24" />
              <path d="m14.83 9.17 4.24-4.24" />
              <path d="m14.83 14.83 4.24 4.24" />
              <path d="m9.17 14.83-4.24 4.24" />
              <circle cx="12" cy="12" r="4" />
            </svg>
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Toetav
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Kui vajad abi, saad juristilt nõu ja tuge kogu protsessi vältel
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" />
              <path d="M6.376 18.91a6 6 0 0 1 11.249.003" />
              <circle cx="12" cy="11" r="4" />
            </svg>
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Turvaline
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Sinu andmeid on kaitstud ja kasutame neid ainult dokumendi koostamiseks
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

        </div>
        <!-- End Grid -->
      </div>
    </div>


    <!-- //SECTION - Hinnad -->
    <!-- Hero -->
    <div class="relative py-10 md:py-14 px-4 before:absolute before:inset-0 before:-z-1 before:mx-3 lg:before:mx-6 2xl:before:mx-10 before:rounded-2xl dark:before:from-neutral-800">
      <div class="max-w-6xl px-4 sm:px-6 lg:px-8 mx-auto">

        <!-- Heading -->
        <div class="mb-4 md:mb-4 max-w-xl mx-auto text-center">
          <h2 class="font-semibold text-4xl">
            Hinnad
          </h2>
          <p class="mt-1 text-lg text-gray-600" style="display: none;">Vali sobiv pakett</p>
        </div>
        <!-- End Heading -->

        <!-- Switch -->
        <div id="toggle-count" class="flex justify-center items-center gap-x-3" style="display: none;">
          <label for="pricing-switch" class="text-sm text-gray-800 dark:text-neutral-200">Monthly</label>
          <label for="pricing-switch" class="relative inline-block w-11 h-6 cursor-pointer">
            <input type="checkbox" id="pricing-switch" class="peer sr-only" checked>
            <span class="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-blue-600 dark:bg-neutral-700 dark:peer-checked:bg-blue-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"></span>
            <span class="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-sm transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"></span>
          </label>
          <label for="pricing-switch" class="text-sm text-gray-800 dark:text-neutral-200">Annually</label>
        </div>
        <!-- End Switch -->
      </div>

      <div class="my-8">
        <div class="max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
          <!-- Grid -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- //LINK - Automaatne -->
            <!-- Card -->
            <div class="p-4 md:p-6 h-full flex flex-col bg-white border border-gray-200 rounded-xl shadow-xs dark:bg-neutral-900 dark:border-neutral-700">
              <!-- Header -->
              <header class="flex flex-col">
                <h4 class="font-semibold text-xl text-gray-800 dark:text-neutral-200">
                  Automaatne
                </h4>

                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-neutral-500">
                    Automaatselt koostatud elatise dokument
                  </p>
                </div>
              </header>
              <!-- End Header -->

              <!-- Body -->
              <div class="flex flex-col">
                <!-- Price -->
                <div class="mt-4 flex items-start gap-x-1">
                  <span class="font-semibold text-gray-800 text-xl dark:text-neutral-200">€</span>
                  <span data-hs-toggle-count='{
                "target": "#toggle-count",
                "min": 0,
                "max": 0
              }' class="font-semibold text-3xl md:text-4xl text-gray-800 dark:text-neutral-200">
                    39
                  </span>
                  <span class="block mt-0.5 text-gray-800 dark:text-neutral-200">
                    EUR
                  </span>
                </div>
                <!-- End Price -->

                <p class="text-xs text-gray-500 dark:text-neutral-500">
                  /dokumendi hind
                </p>

                <div class="mt-5 pb-7 border-b border-gray-200 dark:border-neutral-700">
                  <button type="button" class="py-2 px-2.5 w-full inline-flex justify-center items-center gap-x-1.5 whitespace-nowrap text-[13px] md:text-sm rounded-lg shadow-md bg-orange-500 text-white hover:bg-orange-500 hover:shadow-none focus:outline-hidden focus:bg-orange-600 focus:shadow-none disabled:opacity-50 disabled:pointer-events-none">
                    Alusta siit
                  </button>
                </div>

                <!-- List -->
                <ul class="mt-7 space-y-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Automaatne elatise dokument
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Dokumendi maht 1-5 lehekülge
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Dokument kohe valmis kasutamiseks
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue 1-2 alaealisele lapsele
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue miinimummääras
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi kirjalikud juhised
                    </span>
                  </li>

                </ul>
                <!-- End List -->
              </div>
              <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- //LINK - Lihtne -->
            <!-- Card -->
            <div class="p-4 md:p-6 h-full flex flex-col bg-white border border-gray-200 rounded-xl shadow-xs dark:bg-neutral-900 dark:border-neutral-700">
              <!-- Header -->
              <header class="flex flex-col">
                <h4 class="font-semibold text-xl text-gray-800 dark:text-neutral-200">
                  Lihtne
                </h4>

                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-neutral-500">
                    Elatise dokument juristi juhistega
                  </p>
                </div>
              </header>
              <!-- End Header -->

              <!-- Body -->
              <div class="flex flex-col">
                <!-- Price -->
                <div class="mt-4 flex items-start gap-x-1">
                  <span class="font-semibold text-gray-800 text-xl dark:text-neutral-200">€</span>
                  <span data-hs-toggle-count='{
                "target": "#toggle-count",
                "min": 15,
                "max": 30
              }' class="font-semibold text-3xl md:text-4xl text-gray-800 dark:text-neutral-200">
                    69
                  </span>
                  <span class="block mt-0.5 text-gray-800 dark:text-neutral-200">
                    EUR
                  </span>
                </div>
                <!-- End Price -->

                <p class="text-xs text-gray-500 dark:text-neutral-500">
                  /hind kokku
                </p>

                <div class="mt-5 pb-7 border-b border-gray-200 dark:border-neutral-700">
                  <button type="button" class="py-2 px-2.5 w-full inline-flex justify-center items-center gap-x-1.5 whitespace-nowrap text-[13px] md:text-sm rounded-lg shadow-md bg-orange-500 text-white hover:bg-orange-500 hover:shadow-none focus:outline-hidden focus:bg-orange-600 focus:shadow-none disabled:opacity-50 disabled:pointer-events-none">
                    Alusta siit
                  </button>
                </div>

                <!-- List -->
                <ul class="mt-7 space-y-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi koostatud elatise dokument
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Dokumendi maht 1-5 lehekülge
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Alustamine 5 päeva jooksul
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue 1-3 alaealisele lapsele
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue miinimummääras
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi kirjalikud juhised
                    </span>
                  </li>

                </ul>
                <!-- End List -->
              </div>
              <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- //LINK - Standard -->
            <!-- Card -->
            <div class="p-4 md:p-6 h-full flex flex-col bg-white border border-gray-200 rounded-xl shadow-xs dark:bg-neutral-900 dark:border-neutral-700">
              <!-- Header -->
              <header class="flex flex-col">
                <h4 class="font-semibold text-xl text-gray-800 dark:text-neutral-200">
                  Standard<span class="font-mono font-normal text-xs text-orange-600 dark:text-orange-500" style="display: none;"> — Populaarne</span>
                </h4>

                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-neutral-500">
                    Elatise dokument juristi konsultatsiooniga
                  </p>
                </div>
              </header>
              <!-- End Header -->

              <!-- Body -->
              <div class="flex flex-col">
                <!-- Price -->
                <div class="mt-4 flex items-start gap-x-1">
                  <span class="font-semibold text-gray-800 text-xl dark:text-neutral-200">€</span>
                  <span data-hs-toggle-count='{
                "target": "#toggle-count",
                "min": 45,
                "max": 90
              }' class="font-semibold text-3xl md:text-4xl text-gray-800 dark:text-neutral-200">
                    99
                  </span>
                  <span class="block mt-0.5 text-gray-800 dark:text-neutral-200">
                    EUR
                  </span>
                </div>
                <!-- End Price -->

                <p class="text-xs text-gray-500 dark:text-neutral-500">
                  /hind kokku
                </p>

                <div class="mt-5 pb-7 border-b border-gray-200 dark:border-neutral-700">
                  <button type="button" class="py-2 px-2.5 w-full inline-flex justify-center items-center gap-x-1.5 whitespace-nowrap text-[13px] md:text-sm rounded-lg shadow-md bg-orange-500 text-white hover:bg-orange-500 hover:shadow-none focus:outline-hidden focus:bg-orange-600 focus:shadow-none disabled:opacity-50 disabled:pointer-events-none">
                    Alusta siit
                  </button>
                </div>

                <!-- List -->
                <ul class="mt-7 space-y-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi koostatud elatise dokument
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Dokumendi maht 1-7 lehekülge
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Alustamine 3 päeva jooksul
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue 1-3 alaealisele lapsele
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue miinimummääras
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi konsultatsioon
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatisvõla nõudmine
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatisabi taotlemine
                    </span>
                  </li>
                </ul>
                <!-- End List -->
              </div>
              <!-- End Body -->
            </div>
            <!-- End Card -->

            <!-- //LINK - Personaalne -->
            <!-- Card -->
            <div class="p-4 md:p-6 h-full flex flex-col bg-white border border-gray-200 rounded-xl shadow-xs dark:bg-neutral-900 dark:border-neutral-700">
              <!-- Header -->
              <header class="flex flex-col">
                <h4 class="font-semibold text-xl text-gray-800 dark:text-neutral-200">
                  Personaalne
                </h4>

                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-neutral-500">
                    Elatise dokument juristi nõustamisega
                  </p>
                </div>
              </header>
              <!-- End Header -->

              <!-- Body -->
              <div class="flex flex-col">
                <!-- Price -->
                <div class="mt-4 flex items-start gap-x-1">
                  <span class="font-semibold text-gray-800 text-xl dark:text-neutral-200">€</span>
                  <span data-hs-toggle-count='{
                "target": "#toggle-count",
                "min": 199,
                "max": 399
              }' class="font-semibold text-3xl md:text-4xl text-gray-800 dark:text-neutral-200">
                    199
                  </span>
                  <span class="block mt-0.5 text-gray-800 dark:text-neutral-200">
                    EUR
                  </span>
                </div>
                <!-- End Price -->

                <p class="text-xs text-gray-500 dark:text-neutral-500">
                  /hind kokku
                </p>

                <div class="mt-5 pb-7 border-b border-gray-200 dark:border-neutral-700">
                  <button type="button" class="py-2 px-2.5 w-full inline-flex justify-center items-center gap-x-1.5 whitespace-nowrap text-[13px] md:text-sm rounded-lg shadow-md bg-orange-500 text-white hover:bg-orange-500 hover:shadow-none focus:outline-hidden focus:bg-orange-600 focus:shadow-none disabled:opacity-50 disabled:pointer-events-none ">
                    Alusta siit
                  </button>
                </div>

                <!-- List -->
                <ul class="mt-7 space-y-2.5 text-sm">
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi koostatud elatise dokument
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Dokumendi maht 1-7 lehekülge
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Alustamine 1 päeva jooksul
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise nõue 1-3 alaealisele lapsele
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Vajadusel elatise nõue üle miinimumi
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Juristi nõustamine menetluse ajal
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatisvõla nõudmine
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatisabi taotlemine
                    </span>
                  </li>
                  <li class="flex items-center gap-x-3">
                    <svg class="flex-shrink-0 mt-0.5 h-4 w-4 text-green-600 dark:text-green-600"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 6 9 17l-5-5" />
                    </svg>
                    <span class="text-sm text-gray-800 dark:text-neutral-200">
                      Elatise suurendamine
                    </span>
                  </li>
                </ul>
                <!-- End List -->
              </div>
              <!-- End Body -->
            </div>
            <!-- End Card -->
          </div>
          <!-- End Grid -->
        </div>
      </div>

      <div class="max-w-2xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Card -->
        <div class="p-4 bg-white border border-gray-200 rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
          <div class="flex gap-x-4">
            <svg class="shrink-0 size-7 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10" />
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
              <path d="M12 17h.01" />
            </svg>
            <div class="grow">
              <div class="flex flex-col sm:flex-row sm:items-center gap-3">
                <div class="grow">
                  <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                    Pole kindel, millist paketti valida?
                  </h4>
                  <p class="text-sm text-gray-500 dark:text-neutral-500">
                    Vaata detailset võrdlustabelit ja leia sobiv lahendus.
                  </p>
                </div>
                <div>
                  <a class="py-2 px-2.5 inline-flex items-center gap-x-1.5 whitespace-nowrap text-[13px] md:text-sm rounded-lg bg-white border border-gray-200 text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="mailto:<EMAIL>">
                    Vaata lähemalt
                  </a>

                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- End Card -->
      </div>
    </div>
    <!-- End Hero -->
    <!-- //!SECTION - Hinnad -->

    <!-- //LINK - Kliendid -->
    <!-- Heading -->
    <div class="max-w-xl mx-auto text-center">
      <h2 class="font-semibold text-4xl mb-1">
        Kliendid
      </h2>
      <p class="mt-1 text-lg text-gray-600" style="display: none;">Vali sobiv pakett</p>
    </div>
    <!-- End Heading --> 


    <!-- Slider -->
    <div class="relative py-10 md:pt-20 px-4 before:absolute before:inset-0 before:-z-1 dark:before:from-neutral-800">
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <div data-hs-carousel='{
    "loadingClasses": "opacity-0",
    "dotsItemClasses": "hs-carousel-active:bg-blue-700 hs-carousel-active:border-blue-700 size-3 items-center border border-gray-400 rounded-full cursor-pointer dark:border-neutral-600 dark:hs-carousel-active:bg-blue-500 dark:hs-carousel-active:border-blue-500",
    "isAutoPlay": false
  }' class="relative">
          <div class="hs-carousel relative overflow-hidden w-full max-w-4xl mx-auto min-h-96 bg-white rounded-lg">
            <div class="hs-carousel-body absolute top-0 bottom-0 start-0 flex flex-nowrap transition-transform duration-700 opacity-0">
              <div class="hs-carousel-slide">
                <blockquote class="text-center lg:mx-auto lg:w-3/5">
                  <div class="flex justify-center">
                    <div class="mb-3">
                      <img class="w-20 h-20" src="./assets/failid/pildid/kliendid/1.svg" alt="Kasutaja">
                    </div>
                  </div>
                  <div class="">
                    <p class="text-xl font-normal text-gray-800 lg:text-2xl lg:leading-normal dark:text-neutral-200">
                      Aitäh. Sain dokumendid kenasti kohtule edastatud. Suur tänu Teile abi ja toetuse eest!
                    </p>
                  </div>

                  <footer class="mt-6">
                    <div class="font-semibold text-gray-800">Annika L.</div>
                    <div class="text-sm text-gray-500">Lapsevanem Tallinnast</div>
                  </footer>
                </blockquote>
              </div>
              <div class="hs-carousel-slide">
                <blockquote class="text-center lg:mx-auto lg:w-3/5">
                  <div class="flex justify-center">
                    <div class="mb-3">
                      <img class="w-20 h-20" src="./assets/failid/pildid/kliendid/1.svg" alt="Kasutaja">
                    </div>
                  </div>
                  <div class="">
                    <p class="text-xl font-normal text-gray-800 lg:text-2xl lg:leading-normal dark:text-neutral-200">
                      Aitäh. Sain dokumendid kenasti kohtule edastatud. Suur tänu Teile abi ja toetuse eest!
                    </p>
                  </div>

                  <footer class="mt-6">
                    <div class="font-semibold text-gray-800">Annika L.</div>
                    <div class="text-sm text-gray-500">Lapsevanem Tallinnast</div>
                  </footer>
                </blockquote>
              </div>
              <div class="hs-carousel-slide">
                <blockquote class="text-center lg:mx-auto lg:w-3/5">
                  <div class="flex justify-center">
                    <div class="mb-3">
                      <img class="w-20 h-20" src="./assets/failid/pildid/kliendid/1.svg" alt="Kasutaja">
                    </div>
                  </div>
                  <div class="">
                    <p class="text-xl font-normal text-gray-800 lg:text-2xl lg:leading-normal dark:text-neutral-200">
                      Aitäh. Sain dokumendid kenasti kohtule edastatud. Suur tänu Teile abi ja toetuse eest!
                    </p>
                  </div>

                  <footer class="mt-6">
                    <div class="font-semibold text-gray-800">Annika L.</div>
                    <div class="text-sm text-gray-500">Lapsevanem Tallinnast</div>
                  </footer>
                </blockquote>
              </div>
            </div>
          </div>

          <button type="button" class="hs-carousel-prev absolute left-0 top-1/2 -translate-y-1/2 -ml-12 inline-flex justify-center items-center w-11.5 h-11.5 text-gray-800 opacity-50 focus:outline-hidden dark:text-white dark:hover:bg-white/10 dark:focus:bg-white/10">
            <span class="text-2xl" aria-hidden="true">
              <svg class="shrink-0 size-15" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </span>
            <span class="sr-only">Eelmine</span>
          </button>

          <button type="button" class="hs-carousel-next absolute right-0 top-1/2 -translate-y-1/2 -mr-12 inline-flex justify-center items-center w-11.5 h-11.5 text-gray-800 opacity-50 focus:outline-hidden dark:text-white dark:hover:bg-white/10 dark:focus:bg-white/10">
            <span class="sr-only">Järgmine</span>
            <span class="text-2xl" aria-hidden="true">
              <svg class="shrink-0 size-15" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </span>
          </button>



          <div class="alimendid-carousel-pagination flex justify-center absolute bottom-3 start-0 end-0 flex gap-x-2"></div>
        </div>
      </div>
    </div>
    <!-- End Slider -->


    <!-- Slider -->


    <div class="pt-20" style="background: linear-gradient(180deg, #fff 10%, #fff 100%)">
      <!-- //LINK - Numbrid -->
      <!-- Numbrid -->
      <div class="max-w-7xl px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto rounded-xl transition duration-300">

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 lg:gap-2 max-w-6xl mx-auto text-center">
          <div>
            <p class="text-5xl font-semibold text-orange-500">13+</p>
            <p class="text-xl mt-1 text-gray-500">aastat kogemust</p>
          </div>
          <div class="mb-4 sm:mb-0">
            <p class="text-5xl font-semibold text-orange-500">500+</p>
            <p class="text-xl mt-1 text-gray-500">edukat kohtuasja</p>
          </div>
          <div class="mb-4 sm:mb-0">
            <p class="text-5xl font-semibold text-orange-500">2 500+</p>
            <p class="text-xl mt-1 text-gray-500">aidatud lapsevanemat</p>
          </div>
          <div class="mb-4 sm:mb-0">
            <p class="text-5xl font-semibold text-orange-500">100 000€</p>
            <p class="text-xl mt-1 text-gray-500">kätte saadud elatisraha</p>
          </div>
        </div>
      </div>
      <!-- End Numbrid -->
    </div>


    <!-- //LINK - Blogi -->
    <div class="py-14 lg:py-10 w-full max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="max-w-6xl px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
        <!-- Title -->
        <div class="max-w-2xl mx-auto text-center mb-10">
          <h2 class="text-3xl md:text-4xl lg:text-4xl font-semibold md:pb-1">Blogi</h2>
          <p class="mt-1 text-gray-600 text-lg">Alimendid.ee uudised ja blogi</p>
        </div>
        <!-- End Title -->
        <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <?php include './assets/failid/komponendid/et/blogi/3-viimast.php'; ?>
        </div>
        <div class="text-center">
          <a class="mt-5 py-3 px-4 inline-flex items-center gap-x-1 text-md font-medium rounded-full border border-gray-200 bg-white text-orange-500 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none" href="<?php echo $rootPath; ?>et/blogi.php">
            Rohkem postitusi
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6" />
            </svg>
          </a>
        </div>
      </div>
      <!-- End Card -->
    </div>


    <!-- //LINK - Banner -->
    <?php include './assets/failid/komponendid/et/bannerid/avaleht/avaleht.php'; ?>


  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- //LINK - FOOTER -->
  <?php include './assets/failid/komponendid/et/footer.php'; ?>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="./assets/vendor/lodash/lodash.min.js"></script>
  <script src="./assets/vendor/preline/dist/index.js"></script>
  <!-- Clipboard -->
  <script src="./assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="./assets/js/hs-copy-clipboard-helper.js"></script>

  <script>
    window.addEventListener("load", () => {
      (function() {
        const tabsId = "hs-pro-hero-tabs";
        const tabs = HSTabs.getInstance(`#€{tabsId}`, true);
        const scrollNav = HSScrollNav.getInstance(
          "#hs-pro-hero-tabs-scroll",
          true
        );

        tabs.element.on("change", ({
          el
        }) => {
          scrollNav.element.centerElement(el);
        });

        window.addEventListener(
          "resize",
          _.debounce(() => {
            scrollNav.element.centerElement(tabs.element.current);
          }, 100)
        );

        window.addEventListener("change.hs.tab", ({
          detail
        }) => {
          if (detail.payload.tabsId !== tabsId) return false;

          const tabs = document.querySelector("#hs-pro-hero-tabs-scroll");

          window.scrollTo({
            top: tabs.offsetTop,
            behavior: "smooth",
          });
        });
      })();
    });
  </script>

  <!-- Apexcharts JavaScript -->
  <script src="../node_modules/lodash/lodash.min.js"></script>
  <!-- Apexcharts JavaScript -->
  <script src="../node_modules/apexcharts/dist/apexcharts.min.js"></script>
  <!-- Apexcharts Preline Helper JavaScript -->
  <script src="../node_modules/preline/dist/helper-apexcharts.js"></script>
</body>

</html>

</html>